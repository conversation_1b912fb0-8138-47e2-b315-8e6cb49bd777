package scanner

import (
	"context"
	"fmt"
	"log"
	"math/big"
	"strings"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"

	"ipfun-coin-events/config"
	"ipfun-coin-events/models"
)

// ERC20TransferEventSignature is the signature of the ERC20 Transfer event
const ERC20TransferEventSignature = "Transfer(address,address,uint256)"

// ERC20TransferEventTopicHex is the Keccak-256 hash of the ERC20 Transfer event signature
const ERC20TransferEventTopicHex = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"

// BlockchainScanner handles blockchain scanning operations
type BlockchainScanner struct {
	client      *ethclient.Client
	config      *config.BlockchainConfig
	contractABI abi.ABI
}

// NewBlockchainScanner creates a new blockchain scanner instance
func NewBlockchainScanner(cfg *config.BlockchainConfig) (*BlockchainScanner, error) {
	client, err := ethclient.Dial(cfg.RPCURL)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to blockchain: %w", err)
	}

	// Parse the ABI for the Transfer event
	contractABI, err := abi.JSON(strings.NewReader(`[{"anonymous":false,"inputs":[{"indexed":true,"name":"from","type":"address"},{"indexed":true,"name":"to","type":"address"},{"indexed":false,"name":"value","type":"uint256"}],"name":"Transfer","type":"event"}]`))
	if err != nil {
		return nil, fmt.Errorf("failed to parse ABI: %w", err)
	}

	return &BlockchainScanner{
		client:      client,
		config:      cfg,
		contractABI: contractABI,
	}, nil
}

// GetLatestBlockNumber returns the latest block number from the blockchain
func (s *BlockchainScanner) GetLatestBlockNumber(ctx context.Context) (uint64, error) {
	header, err := s.client.HeaderByNumber(ctx, nil)
	if err != nil {
		return 0, fmt.Errorf("failed to get latest block header: %w", err)
	}

	// Subtract confirmation blocks for safety
	latestBlock := header.Number.Uint64()
	if latestBlock > s.config.ConfirmationBlocks {
		latestBlock -= s.config.ConfirmationBlocks
	}

	return latestBlock, nil
}

// DetermineScanRange determines the block range to scan with optional cached toBlock optimization
func (s *BlockchainScanner) DetermineScanRange(ctx context.Context, lastProcessedBlock int, cachedToBlock *uint64) (uint64, uint64, error) {
	latestBlock, err := s.GetLatestBlockNumber(ctx)
	if err != nil {
		return 0, 0, err
	}

	var fromBlock uint64

	// Use cached toBlock for optimization if available
	if cachedToBlock != nil && *cachedToBlock > 0 {
		// Start from (cachedToBlock - 1) to ensure no blocks are missed
		if *cachedToBlock > 1 {
			fromBlock = *cachedToBlock - 1
		} else {
			fromBlock = s.config.InitialBlockHeight
		}
		log.Printf("Using cached toBlock optimization: starting from block %d (cached toBlock was %d)", fromBlock, *cachedToBlock)
	} else if lastProcessedBlock <= 0 {
		// Use initial block height from config
		fromBlock = s.config.InitialBlockHeight
		log.Printf("No cached toBlock or processed blocks, starting from initial block height: %d", fromBlock)
	} else {
		// Start from (lastProcessedBlock - 1) but ensure it's not negative
		if lastProcessedBlock > 1 {
			fromBlock = uint64(lastProcessedBlock - 1)
		} else {
			fromBlock = s.config.InitialBlockHeight
		}
		log.Printf("Using database lastProcessedBlock: starting from block %d", fromBlock)
	}

	// Ensure fromBlock doesn't exceed latestBlock
	if fromBlock > latestBlock {
		return 0, 0, fmt.Errorf("fromBlock (%d) is greater than latestBlock (%d)", fromBlock, latestBlock)
	}

	// Limit scan range to batch size
	if latestBlock-fromBlock > s.config.MaxBatchSize {
		toBlock := fromBlock + s.config.ScanBatchSize - 1
		if toBlock > latestBlock {
			toBlock = latestBlock
		}
		return fromBlock, toBlock, nil
	}

	return fromBlock, latestBlock, nil
}

// ScanTransferEvents scans for ERC20 transfer events in the specified block range
func (s *BlockchainScanner) ScanTransferEvents(ctx context.Context, fromBlock, toBlock uint64, contractAddresses []string) ([]models.TransferEvent, error) {
	log.Printf("Scanning blocks %d to %d for transfer events", fromBlock, toBlock)

	// Convert contract addresses to common.Address slice
	var addresses []common.Address
	for _, addr := range contractAddresses {
		if common.IsHexAddress(addr) {
			addresses = append(addresses, common.HexToAddress(addr))
		}
	}

	// If no valid addresses, return empty result
	if len(addresses) == 0 {
		log.Println("No valid contract addresses to scan")
		return []models.TransferEvent{}, nil
	}

	// Prepare the filter query
	transferEventTopic := common.HexToHash(ERC20TransferEventTopicHex)
	query := ethereum.FilterQuery{
		FromBlock: big.NewInt(int64(fromBlock)),
		ToBlock:   big.NewInt(int64(toBlock)),
		Addresses: addresses,
		Topics: [][]common.Hash{
			{transferEventTopic}, // Topic0: Transfer event signature hash
		},
	}

	// Fetch logs from the blockchain
	logs, err := s.client.FilterLogs(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to filter logs: %w", err)
	}

	log.Printf("Found %d transfer events", len(logs))

	// Parse the logs into TransferEvent structs
	var events []models.TransferEvent
	for _, vLog := range logs {
		event, err := s.parseTransferEvent(vLog)
		if err != nil {
			log.Printf("Failed to parse transfer event in tx %s: %v", vLog.TxHash.Hex(), err)
			continue
		}
		events = append(events, *event)
	}

	return events, nil
}

// parseTransferEvent parses a raw log into a TransferEvent
func (s *BlockchainScanner) parseTransferEvent(vLog types.Log) (*models.TransferEvent, error) {
	// Ensure we have enough topics
	if len(vLog.Topics) < 3 {
		return nil, fmt.Errorf("insufficient topics in log")
	}

	// Extract from and to addresses from topics
	fromAddress := common.BytesToAddress(vLog.Topics[1].Bytes())
	toAddress := common.BytesToAddress(vLog.Topics[2].Bytes())

	// Parse the value from log data
	var eventDataMap = make(map[string]interface{})
	err := s.contractABI.UnpackIntoMap(eventDataMap, "Transfer", vLog.Data)
	if err != nil {
		// Handle empty data case
		if len(vLog.Data) == 0 {
			eventDataMap["value"] = big.NewInt(0)
		} else {
			return nil, fmt.Errorf("failed to unpack log data: %w", err)
		}
	}

	value, ok := eventDataMap["value"].(*big.Int)
	if !ok {
		return nil, fmt.Errorf("failed to assert value to *big.Int")
	}

	// Get block information
	block, err := s.client.BlockByNumber(context.Background(), big.NewInt(int64(vLog.BlockNumber)))
	if err != nil {
		return nil, fmt.Errorf("failed to get block: %w", err)
	}

	return &models.TransferEvent{
		ContractAddress: vLog.Address.Hex(),
		FromAddress:     fromAddress.Hex(),
		ToAddress:       toAddress.Hex(),
		Amount:          value.String(),
		BlockNumber:     vLog.BlockNumber,
		BlockHash:       vLog.BlockHash.Hex(),
		TxHash:          vLog.TxHash.Hex(),
		LogIndex:        vLog.Index,
		TradeTime:       int64(block.Time()),
	}, nil
}

// Close closes the blockchain client connection
func (s *BlockchainScanner) Close() {
	if s.client != nil {
		s.client.Close()
	}
}
