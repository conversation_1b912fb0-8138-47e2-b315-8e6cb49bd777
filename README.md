# IPFun Coin Events Scanner

A blockchain scanning system that monitors ERC20 transfer events for specific coin contracts and stores them in a PostgreSQL database.

## Features

- **Blockchain Scanning**: Monitors blockchain for ERC20 transfer events
- **Database Integration**: Uses GORM with PostgreSQL for data persistence
- **Configuration Management**: TOML-based configuration
- **Active Address Filtering**: Only processes transfers for active coin addresses
- **Duplicate Prevention**: Checks for existing records before insertion
- **Graceful Shutdown**: Handles system signals for clean shutdown
- **Continuous Monitoring**: Configurable scanning intervals

## Architecture

The system consists of several key components:

- **Config**: TOML configuration parsing and management
- **Database**: GORM-based PostgreSQL connection and models
- **Scanner**: Blockchain event scanning using go-ethereum
- **Processor**: Transfer event filtering and database operations
- **Models**: Database table definitions and helper functions

## Prerequisites

- Go 1.20 or higher
- PostgreSQL database
- Ethereum-compatible blockchain node (or RPC endpoint)

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd ipfun-coin-events
```

2. Install dependencies:
```bash
go mod tidy
```

3. Configure the application by editing `config.toml`:
```toml
[database]
host = "localhost"
port = 5432
user = "postgres"
password = "password"
dbname = "ipfun_staging"
sslmode = "disable"
timezone = "UTC"
max_idle_conns = 10
max_open_conns = 100
conn_max_lifetime = "1h"

[blockchain]
rpc_url = "http://localhost:8545"
chain_type = "BASE"
initial_block_height = 1000000
scan_batch_size = 1000
confirmation_blocks = 12

[app]
log_level = "info"
scan_interval = "30s"
```

## Database Setup

The system expects the following PostgreSQL tables to exist:

### t_contrat_coin_transfer_log_test
```sql
CREATE TABLE ip_fun_staging.t_contrat_coin_transfer_log_test (
    id SERIAL PRIMARY KEY,
    ip_coin_address VARCHAR(255) NOT NULL,
    from_address VARCHAR(255) NOT NULL,
    to_address VARCHAR(255) NOT NULL,
    trade_time INTEGER NOT NULL,
    token_amount NUMERIC(100) NOT NULL,
    status INTEGER NOT NULL,
    transaction_hash VARCHAR(255) NOT NULL,
    block_hash VARCHAR(255) NOT NULL,
    block_num INTEGER DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    market_cap VARCHAR(255),
    supply VARCHAR(255),
    chain_type VARCHAR(32) DEFAULT 'BASE' NOT NULL,
    event_id INTEGER
);
```

### t_group
```sql
CREATE TABLE ip_fun_staging.t_group (
    id SERIAL PRIMARY KEY,
    ip_coin_address VARCHAR(255) NOT NULL,
    status INTEGER NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Usage

1. Start the scanner:
```bash
go run main.go
```

2. The scanner will:
   - Load configuration from `config.toml`
   - Connect to the PostgreSQL database
   - Initialize blockchain connection
   - Query active coin addresses (status = 100) from `t_group` table
   - Start scanning from the last processed block or initial block height
   - Process transfer events and store them in the database
   - Continue scanning at configured intervals

## Configuration Options

### Database Configuration
- `host`: PostgreSQL host
- `port`: PostgreSQL port
- `user`: Database username
- `password`: Database password
- `dbname`: Database name
- `sslmode`: SSL mode (disable/require/verify-ca/verify-full)
- `timezone`: Database timezone
- `max_idle_conns`: Maximum idle connections
- `max_open_conns`: Maximum open connections
- `conn_max_lifetime`: Connection maximum lifetime

### Blockchain Configuration
- `rpc_url`: Ethereum RPC endpoint URL
- `chain_type`: Blockchain type (e.g., "BASE")
- `initial_block_height`: Starting block for initial scan
- `scan_batch_size`: Number of blocks to scan per batch
- `confirmation_blocks`: Number of confirmation blocks to wait

### Application Configuration
- `log_level`: Logging level (debug/info/warn/error)
- `scan_interval`: Time between scans

## Testing

Run the tests:
```bash
go test ./...
```

## Logging

The application provides detailed logging including:
- Configuration loading
- Database connection status
- Blockchain scanning progress
- Transfer event processing
- Error handling

## Error Handling

The system includes comprehensive error handling for:
- Configuration loading errors
- Database connection issues
- Blockchain connectivity problems
- Invalid transfer events
- Duplicate record detection

## Graceful Shutdown

The application handles SIGINT and SIGTERM signals for graceful shutdown, ensuring:
- Current scan operations complete
- Database connections are properly closed
- Blockchain connections are terminated cleanly
