package config

import (
	"os"
	"testing"
	"time"
)

func TestLoadConfig(t *testing.T) {
	// Create a temporary config file for testing
	configContent := `
[database]
host = "localhost"
port = 5432
user = "testuser"
password = "testpass"
dbname = "testdb"
sslmode = "disable"
timezone = "UTC"
max_idle_conns = 10
max_open_conns = 100
conn_max_lifetime = "1h"

[blockchain]
rpc_url = "http://localhost:8545"
chain_type = "BASE"
initial_block_height = 1000000
scan_batch_size = 1000
max_batch_size = 10000
confirmation_blocks = 12

[app]
log_level = "info"
scan_interval = "30s"
`

	// Create temporary file
	tmpFile, err := os.CreateTemp("", "config_test_*.toml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	// Write config content
	if _, err := tmpFile.WriteString(configContent); err != nil {
		t.Fatalf("Failed to write config content: %v", err)
	}
	tmpFile.Close()

	// Load config
	cfg, err := LoadConfig(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// Validate database config
	if cfg.Database.Host != "localhost" {
		t.Errorf("Expected host 'localhost', got '%s'", cfg.Database.Host)
	}
	if cfg.Database.Port != 5432 {
		t.Errorf("Expected port 5432, got %d", cfg.Database.Port)
	}
	if cfg.Database.ConnMaxLifetime != time.Hour {
		t.Errorf("Expected conn_max_lifetime 1h, got %v", cfg.Database.ConnMaxLifetime)
	}

	// Validate blockchain config
	if cfg.Blockchain.RPCURL != "http://localhost:8545" {
		t.Errorf("Expected RPC URL 'http://localhost:8545', got '%s'", cfg.Blockchain.RPCURL)
	}
	if cfg.Blockchain.InitialBlockHeight != 1000000 {
		t.Errorf("Expected initial block height 1000000, got %d", cfg.Blockchain.InitialBlockHeight)
	}
	if cfg.Blockchain.MaxBatchSize != 10000 {
		t.Errorf("Expected max batch size 10000, got %d", cfg.Blockchain.MaxBatchSize)
	}

	// Validate app config
	if cfg.App.ScanInterval != 30*time.Second {
		t.Errorf("Expected scan interval 30s, got %v", cfg.App.ScanInterval)
	}
}

func TestLoadConfigWithDefaults(t *testing.T) {
	// Create a temporary config file without max_batch_size for testing defaults
	configContent := `
[database]
host = "localhost"
port = 5432
user = "testuser"
password = "testpass"
dbname = "testdb"
sslmode = "disable"
timezone = "UTC"
max_idle_conns = 10
max_open_conns = 100
conn_max_lifetime = "1h"

[blockchain]
rpc_url = "http://localhost:8545"
chain_type = "BASE"
initial_block_height = 1000000
scan_batch_size = 1000
confirmation_blocks = 12

[app]
log_level = "info"
scan_interval = "30s"
`

	// Create temporary file
	tmpFile, err := os.CreateTemp("", "config_defaults_test_*.toml")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	// Write config content
	if _, err := tmpFile.WriteString(configContent); err != nil {
		t.Fatalf("Failed to write config content: %v", err)
	}
	tmpFile.Close()

	// Load config
	cfg, err := LoadConfig(tmpFile.Name())
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// Validate that default max_batch_size is set
	if cfg.Blockchain.MaxBatchSize != 10000 {
		t.Errorf("Expected default max batch size 10000, got %d", cfg.Blockchain.MaxBatchSize)
	}
}

func TestGetDSN(t *testing.T) {
	// Test without schema
	dbConfig := DatabaseConfig{
		Host:     "localhost",
		Port:     5432,
		User:     "testuser",
		Password: "testpass",
		DBName:   "testdb",
		SSLMode:  "disable",
		TimeZone: "UTC",
	}

	expectedDSN := "host=localhost port=5432 user=testuser password=testpass dbname=testdb sslmode=disable TimeZone=UTC"
	actualDSN := dbConfig.GetDSN()

	if actualDSN != expectedDSN {
		t.Errorf("Expected DSN '%s', got '%s'", expectedDSN, actualDSN)
	}

	// Test with schema
	dbConfigWithSchema := DatabaseConfig{
		Host:     "localhost",
		Port:     5432,
		User:     "testuser",
		Password: "testpass",
		DBName:   "testdb",
		Schema:   "ip_fun_staging",
		SSLMode:  "disable",
		TimeZone: "UTC",
	}

	expectedDSNWithSchema := "host=localhost port=5432 user=testuser password=testpass dbname=testdb sslmode=disable TimeZone=UTC search_path=ip_fun_staging"
	actualDSNWithSchema := dbConfigWithSchema.GetDSN()

	if actualDSNWithSchema != expectedDSNWithSchema {
		t.Errorf("Expected DSN with schema '%s', got '%s'", expectedDSNWithSchema, actualDSNWithSchema)
	}
}
