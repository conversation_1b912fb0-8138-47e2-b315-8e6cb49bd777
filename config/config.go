package config

import (
	"fmt"
	"time"

	"github.com/BurntSushi/toml"
)

// Config represents the application configuration
type Config struct {
	Database   DatabaseConfig   `toml:"database"`
	Blockchain BlockchainConfig `toml:"blockchain"`
	App        AppConfig        `toml:"app"`
}

// DatabaseConfig holds database connection settings
type DatabaseConfig struct {
	Host            string        `toml:"host"`
	Port            int           `toml:"port"`
	User            string        `toml:"user"`
	Password        string        `toml:"password"`
	DBName          string        `toml:"dbname"`
	Schema          string        `toml:"schema"`
	SSLMode         string        `toml:"sslmode"`
	TimeZone        string        `toml:"timezone"`
	MaxIdleConns    int           `toml:"max_idle_conns"`
	MaxOpenConns    int           `toml:"max_open_conns"`
	ConnMaxLifetime time.Duration `toml:"conn_max_lifetime"`
}

// BlockchainConfig holds blockchain connection and scanning settings
type BlockchainConfig struct {
	RPCURL             string `toml:"rpc_url"`
	ChainType          string `toml:"chain_type"`
	InitialBlockHeight uint64 `toml:"initial_block_height"`
	ScanBatchSize      uint64 `toml:"scan_batch_size"`
	MaxBatchSize       uint64 `toml:"max_batch_size"`
	ConfirmationBlocks uint64 `toml:"confirmation_blocks"`
}

// AppConfig holds general application settings
type AppConfig struct {
	LogLevel     string        `toml:"log_level"`
	ScanInterval time.Duration `toml:"scan_interval"`
}

// LoadConfig loads configuration from a TOML file
func LoadConfig(filename string) (*Config, error) {
	var config Config

	if _, err := toml.DecodeFile(filename, &config); err != nil {
		return nil, err
	}

	// Set default values if not specified
	setDefaults(&config)

	return &config, nil
}

// setDefaults sets default values for configuration fields
func setDefaults(config *Config) {
	// Set default max_batch_size if not specified
	if config.Blockchain.MaxBatchSize == 0 {
		config.Blockchain.MaxBatchSize = 10000
	}
}

// GetDSN returns the PostgreSQL DSN string
func (db *DatabaseConfig) GetDSN() string {
	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s TimeZone=%s",
		db.Host, db.Port, db.User, db.Password, db.DBName, db.SSLMode, db.TimeZone)

	// Add search_path if schema is specified
	if db.Schema != "" {
		dsn += fmt.Sprintf(" search_path=%s", db.Schema)
	}

	return dsn
}
