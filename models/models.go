package models

import (
	"time"

	"gorm.io/gorm"
)

// ContractCoinTransferLog represents the t_contrat_coin_transfer_log table
type ContractCoinTransferLog struct {
	ID              uint      `gorm:"primaryKey;column:id" json:"id"`
	IPCoinAddress   string    `gorm:"column:ip_coin_address;not null" json:"ip_coin_address"`
	FromAddress     string    `gorm:"column:from_address;not null" json:"from_address"`
	ToAddress       string    `gorm:"column:to_address;not null" json:"to_address"`
	TradeTime       int       `gorm:"column:trade_time;not null" json:"trade_time"`
	TokenAmount     string    `gorm:"column:token_amount;type:numeric(100);not null" json:"token_amount"`
	Status          int       `gorm:"column:status;not null" json:"status"`
	TransactionHash string    `gorm:"column:transaction_hash;not null" json:"transaction_hash"`
	BlockHash       string    `gorm:"column:block_hash;not null" json:"block_hash"`
	BlockNum        int       `gorm:"column:block_num;default:0" json:"block_num"`
	CreateTime      time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP" json:"create_time"`
	UpdateTime      time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP" json:"update_time"`
	MarketCap       *string   `gorm:"column:market_cap" json:"market_cap"`
	Supply          *string   `gorm:"column:supply" json:"supply"`
	ChainType       string    `gorm:"column:chain_type;default:'BASE';not null" json:"chain_type"`
	EventID         *int      `gorm:"column:event_id" json:"event_id"`
}

// TableName specifies the table name for GORM
func (ContractCoinTransferLog) TableName() string {
	return "t_contrat_coin_transfer_log_test"
}

// Group represents the t_group table for active coin addresses
type Group struct {
	ID            uint      `gorm:"primaryKey;column:id" json:"id"`
	IPCoinAddress string    `gorm:"column:ip_coin_address;not null" json:"ip_coin_address"`
	Status        int       `gorm:"column:status;not null" json:"status"`
	CreateTime    time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP" json:"create_time"`
	UpdateTime    time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP" json:"update_time"`
}

// TableName specifies the table name for GORM
func (Group) TableName() string {
	return "t_group"
}

// TransferEvent represents a blockchain transfer event
type TransferEvent struct {
	ContractAddress string
	FromAddress     string
	ToAddress       string
	Amount          string
	BlockNumber     uint64
	BlockHash       string
	TxHash          string
	LogIndex        uint
	TradeTime       int64
}

// GetActiveCoinAddresses retrieves all active coin addresses from t_group table
func GetActiveCoinAddresses(db *gorm.DB) ([]string, error) {
	var groups []Group
	err := db.Where("status = ?", 100).Find(&groups).Error
	if err != nil {
		return nil, err
	}

	addresses := make([]string, len(groups))
	for i, group := range groups {
		addresses[i] = group.IPCoinAddress
	}

	return addresses, nil
}

// GetLatestBlockNumber retrieves the highest block_num from the transfer log table
func GetLatestBlockNumber(db *gorm.DB) (int, error) {
	var result struct {
		MaxBlockNum int
	}

	err := db.Model(&ContractCoinTransferLog{}).
		Select("COALESCE(MAX(block_num), 0) as max_block_num").
		Scan(&result).Error

	if err != nil {
		return 0, err
	}

	return result.MaxBlockNum, nil
}

// TransferLogExists checks if a transfer log already exists based on unique constraints
func TransferLogExists(db *gorm.DB, txHash, fromAddr, toAddr string, eventID *int) (bool, error) {
	var count int64
	query := db.Model(&ContractCoinTransferLog{}).
		Where("transaction_hash = ? AND from_address = ? AND to_address = ?", txHash, fromAddr, toAddr)

	if eventID != nil {
		query = query.Where("event_id = ?", *eventID)
	} else {
		query = query.Where("event_id IS NULL")
	}

	err := query.Count(&count).Error
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// CreateTransferLog creates a new transfer log record
func CreateTransferLog(db *gorm.DB, log *ContractCoinTransferLog) error {
	return db.Create(log).Error
}

// CreateTransferLogBatch creates multiple transfer log records in a single transaction
func CreateTransferLogBatch(db *gorm.DB, logs []*ContractCoinTransferLog) error {
	if len(logs) == 0 {
		return nil
	}

	return db.Transaction(func(tx *gorm.DB) error {
		return tx.CreateInBatches(logs, 100).Error
	})
}
