# IPFun Coin Events Scanner - 使用示例

## 快速开始

### 1. 准备数据库

首先，确保你有一个运行中的 PostgreSQL 数据库，并创建必要的表：

```sql
-- 创建 schema
CREATE SCHEMA IF NOT EXISTS ip_fun_staging;

-- 创建转账记录表
CREATE TABLE ip_fun_staging.t_contrat_coin_transfer_log_test (
    id SERIAL PRIMARY KEY,
    ip_coin_address VARCHAR(255) NOT NULL,
    from_address VARCHAR(255) NOT NULL,
    to_address VARCHAR(255) NOT NULL,
    trade_time INTEGER NOT NULL,
    token_amount NUMERIC(100) NOT NULL,
    status INTEGER NOT NULL,
    transaction_hash VARCHAR(255) NOT NULL,
    block_hash VARCHAR(255) NOT NULL,
    block_num INTEGER DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    market_cap VARCHAR(255),
    supply VARCHAR(255),
    chain_type VARCHAR(32) DEFAULT 'BASE' NOT NULL,
    event_id INTEGER
);

-- 创建组表（用于存储活跃的币种地址）
CREATE TABLE ip_fun_staging.t_group (
    id SERIAL PRIMARY KEY,
    ip_coin_address VARCHAR(255) NOT NULL,
    status INTEGER NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入一些测试数据（活跃的币种地址）
INSERT INTO ip_fun_staging.t_group (ip_coin_address, status) VALUES
('0x1234567890123456789012345678901234567890', 100),
('0xabcdefabcdefabcdefabcdefabcdefabcdefabcd', 100);
```

### 2. 配置应用

编辑 `config.toml` 文件：

```toml
[database]
host = "localhost"
port = 5432
user = "your_username"
password = "your_password"
dbname = "your_database"
sslmode = "disable"
timezone = "UTC"
max_idle_conns = 10
max_open_conns = 100
conn_max_lifetime = "1h"

[blockchain]
rpc_url = "https://mainnet.base.org"  # 或者你的 BASE 网络 RPC 端点
chain_type = "BASE"
initial_block_height = 10000000  # 设置一个合适的起始区块
scan_batch_size = 1000
confirmation_blocks = 12

[app]
log_level = "info"
scan_interval = "30s"
```

### 3. 运行扫描器

```bash
# 构建应用
go build -o ipfun-scanner .

# 运行扫描器
./ipfun-scanner
```

### 4. 预期输出

当扫描器运行时，你会看到类似以下的日志输出：

```
2024/06/21 23:23:00 Starting IPFun Coin Events Scanner...
2024/06/21 23:23:00 Database connection established successfully
2024/06/21 23:23:00 Schema ip_fun_staging created or already exists
2024/06/21 23:23:00 Scanner started, scanning every 30s
2024/06/21 23:23:00 Starting blockchain scan...
2024/06/21 23:23:00 Last processed block: 0
2024/06/21 23:23:00 Scanning blocks from 10000000 to 10001000
2024/06/21 23:23:00 Found 2 active coin addresses
2024/06/21 23:23:01 Found 5 transfer events
2024/06/21 23:23:01 Processed transfer event: tx=0x..., from=0x..., to=0x..., amount=1000000000000000000, block=10000123
2024/06/21 23:23:01 Transfer processing completed: processed=3, skipped=2, total=5
2024/06/21 23:23:01 Scan completed successfully for blocks 10000000 to 10001000
```

## 系统工作流程

1. **启动阶段**：
   - 加载配置文件
   - 连接数据库
   - 初始化区块链客户端

2. **扫描循环**：
   - 查询数据库中最后处理的区块号
   - 确定扫描范围（从 `last_block - 1` 到当前最新区块）
   - 从 `t_group` 表获取活跃币种地址（status = 100）
   - 扫描指定区块范围内的 ERC20 转账事件
   - 过滤只包含活跃币种地址的转账
   - 检查重复记录并插入新的转账记录

3. **数据处理**：
   - 解析转账事件（from, to, amount, block info）
   - 检查是否已存在相同的转账记录
   - 将新记录插入 `t_contrat_coin_transfer_log_test` 表

## 故障排除

### 常见问题

1. **数据库连接失败**：
   - 检查数据库配置是否正确
   - 确保数据库服务正在运行
   - 验证用户权限

2. **区块链连接失败**：
   - 检查 RPC URL 是否可访问
   - 验证网络连接
   - 确认 RPC 端点支持所需的方法

3. **没有找到转账事件**：
   - 检查 `t_group` 表中是否有 status = 100 的记录
   - 验证币种地址格式是否正确
   - 确认扫描的区块范围内确实有转账活动

### 调试技巧

1. **启用详细日志**：
   ```toml
   [app]
   log_level = "debug"
   ```

2. **减小扫描批次大小**：
   ```toml
   [blockchain]
   scan_batch_size = 100
   ```

3. **检查数据库记录**：
   ```sql
   -- 查看最新的转账记录
   SELECT * FROM ip_fun_staging.t_contrat_coin_transfer_log_test
   ORDER BY create_time DESC LIMIT 10;

   -- 查看活跃币种地址
   SELECT * FROM ip_fun_staging.t_group WHERE status = 100;
   ```

## 生产环境部署建议

1. **使用环境变量**：考虑将敏感信息（如数据库密码）存储在环境变量中
2. **监控和告警**：设置日志监控和错误告警
3. **备份策略**：定期备份数据库
4. **性能优化**：根据实际负载调整连接池和扫描参数
5. **高可用性**：考虑使用多个 RPC 端点和数据库主从复制
