package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"ipfun-coin-events/config"
	"ipfun-coin-events/database"
	"ipfun-coin-events/processor"
	"ipfun-coin-events/scanner"
)

func main() {
	log.Println("Starting IPFun Coin Events Scanner...")

	// Load configuration
	cfg, err := config.LoadConfig("config.toml")
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize database connection
	err = database.InitDatabase(&cfg.Database)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer func() {
		if err := database.CloseDatabase(); err != nil {
			log.Printf("Error closing database: %v", err)
		}
	}()

	// Initialize blockchain scanner
	blockchainScanner, err := scanner.NewBlockchainScanner(&cfg.Blockchain)
	if err != nil {
		log.Fatalf("Failed to initialize blockchain scanner: %v", err)
	}
	defer blockchainScanner.Close()

	// Initialize transfer processor
	transferProcessor := processor.NewTransferProcessor(database.GetDB())

	// Initialize block cache for optimization
	var cachedToBlock *uint64

	// Setup graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle shutdown signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		log.Println("Received shutdown signal, stopping scanner...")
		cancel()
	}()

	// Start the scanning loop
	ticker := time.NewTicker(cfg.App.ScanInterval)
	defer ticker.Stop()

	log.Printf("Scanner started, scanning every %v", cfg.App.ScanInterval)

	// Run initial scan
	toBlock, err := performScan(ctx, blockchainScanner, transferProcessor, &cfg.Blockchain, cachedToBlock)
	if err != nil {
		log.Printf("Initial scan failed: %v", err)
	} else if toBlock != nil {
		cachedToBlock = toBlock
		log.Printf("Cached toBlock for next scan: %d", *cachedToBlock)
	}

	// Continue scanning at intervals
	for {
		select {
		case <-ctx.Done():
			log.Println("Scanner stopped")
			return
		case <-ticker.C:
			toBlock, err := performScan(ctx, blockchainScanner, transferProcessor, &cfg.Blockchain, cachedToBlock)
			if err != nil {
				log.Printf("Scan failed: %v", err)
			} else if toBlock != nil {
				cachedToBlock = toBlock
				log.Printf("Cached toBlock for next scan: %d", *cachedToBlock)
			}
		}
	}
}

func performScan(ctx context.Context, scanner *scanner.BlockchainScanner, processor *processor.TransferProcessor, cfg *config.BlockchainConfig, cachedToBlock *uint64) (*uint64, error) {
	log.Println("Starting blockchain scan...")

	// Get the latest processed block number from database
	lastProcessedBlock, err := processor.GetLatestBlockNumber()
	if err != nil {
		return nil, err
	}

	log.Printf("Last processed block: %d", lastProcessedBlock)

	// Determine scan range with cached toBlock optimization
	fromBlock, toBlock, err := scanner.DetermineScanRange(ctx, lastProcessedBlock, cachedToBlock)
	if err != nil {
		return nil, err
	}

	if fromBlock > toBlock {
		log.Printf("No new blocks to scan (from: %d, to: %d)", fromBlock, toBlock)
		return &toBlock, nil
	}

	log.Printf("Scanning blocks from %d to %d", fromBlock, toBlock)

	// Get active coin addresses
	activeCoinAddresses, err := processor.GetActiveCoinAddresses()
	if err != nil {
		return nil, err
	}

	if len(activeCoinAddresses) == 0 {
		log.Println("No active coin addresses found, skipping scan")
		return &toBlock, nil
	}

	log.Printf("Found %d active coin addresses", len(activeCoinAddresses))

	// Scan for transfer events
	events, err := scanner.ScanTransferEvents(ctx, fromBlock, toBlock, activeCoinAddresses)
	if err != nil {
		return nil, err
	}

	if len(events) == 0 {
		log.Printf("No transfer events found in blocks %d to %d", fromBlock, toBlock)
		return &toBlock, nil
	}

	log.Printf("Found %d transfer events", len(events))

	// Process the transfer events
	err = processor.ProcessTransferEvents(events, activeCoinAddresses)
	if err != nil {
		return nil, err
	}

	log.Printf("Scan completed successfully for blocks %d to %d", fromBlock, toBlock)
	return &toBlock, nil
}
