package processor

import (
	"fmt"
	"log"

	"gorm.io/gorm"

	"ipfun-coin-events/models"
)

// TransferProcessor handles the processing of transfer events
type TransferProcessor struct {
	db *gorm.DB
}

// NewTransferProcessor creates a new transfer processor
func NewTransferProcessor(db *gorm.DB) *TransferProcessor {
	return &TransferProcessor{
		db: db,
	}
}

// ProcessTransferEvents processes a batch of transfer events using batch transaction
func (p *TransferProcessor) ProcessTransferEvents(events []models.TransferEvent, activeCoinAddresses []string) error {
	if len(events) == 0 {
		log.Println("No transfer events to process")
		return nil
	}

	// Create a map for faster lookup of active coin addresses
	activeAddressMap := make(map[string]bool)
	for _, addr := range activeCoinAddresses {
		activeAddressMap[addr] = true
	}

	// First pass: filter and validate events, collect new records to insert
	var newTransferLogs []*models.ContractCoinTransferLog
	skippedCount := 0

	for _, event := range events {
		// Check if the contract address is in the active coin addresses
		if !activeAddressMap[event.ContractAddress] {
			skippedCount++
			continue
		}

		var eventId = int(event.LogIndex)

		// Check if the record already exists
		exists, err := models.TransferLogExists(p.db, event.TxHash, event.FromAddress, event.ToAddress, &eventId)
		if err != nil {
			log.Printf("Error checking if transfer log exists: %v", err)
			skippedCount++
			continue
		}

		if exists {
			log.Printf("Transfer log already exists for tx %s, skipping", event.TxHash)
			skippedCount++
			continue
		}

		// Create new transfer log record
		transferLog := &models.ContractCoinTransferLog{
			IPCoinAddress:   event.ContractAddress,
			FromAddress:     event.FromAddress,
			ToAddress:       event.ToAddress,
			TradeTime:       int(event.TradeTime),
			TokenAmount:     event.Amount,
			Status:          0, // Default status
			TransactionHash: event.TxHash,
			BlockHash:       event.BlockHash,
			BlockNum:        int(event.BlockNumber),
			MarketCap:       nil,
			Supply:          nil,
			ChainType:       "BSC", // Default chain type
			EventID:         &eventId,
		}

		newTransferLogs = append(newTransferLogs, transferLog)
	}

	// Second pass: batch insert all new records in a single transaction
	processedCount := 0
	if len(newTransferLogs) > 0 {
		err := models.CreateTransferLogBatch(p.db, newTransferLogs)
		if err != nil {
			log.Printf("Error batch creating transfer logs: %v", err)
			// Fallback to individual inserts if batch fails
			return p.fallbackIndividualInserts(newTransferLogs)
		}
		processedCount = len(newTransferLogs)

		// Log some sample processed events
		for i, transferLog := range newTransferLogs {
			if i < 5 { // Log first 5 events
				log.Printf("Processed transfer event: tx=%s, from=%s, to=%s, amount=%s, block=%d",
					transferLog.TransactionHash, transferLog.FromAddress, transferLog.ToAddress,
					transferLog.TokenAmount, transferLog.BlockNum)
			}
		}
		if len(newTransferLogs) > 5 {
			log.Printf("... and %d more events", len(newTransferLogs)-5)
		}
	}

	log.Printf("Transfer processing completed: processed=%d, skipped=%d, total=%d",
		processedCount, skippedCount, len(events))

	return nil
}

// GetActiveCoinAddresses retrieves active coin addresses from the database
func (p *TransferProcessor) GetActiveCoinAddresses() ([]string, error) {
	return models.GetActiveCoinAddresses(p.db)
}

// GetLatestBlockNumber retrieves the latest processed block number from the database
func (p *TransferProcessor) GetLatestBlockNumber() (int, error) {
	return models.GetLatestBlockNumber(p.db)
}

// FilterTransfersByActiveAddresses filters transfer events by active coin addresses
func (p *TransferProcessor) FilterTransfersByActiveAddresses(events []models.TransferEvent, activeCoinAddresses []string) []models.TransferEvent {
	if len(activeCoinAddresses) == 0 {
		log.Println("No active coin addresses found, returning empty result")
		return []models.TransferEvent{}
	}

	// Create a map for faster lookup
	activeAddressMap := make(map[string]bool)
	for _, addr := range activeCoinAddresses {
		activeAddressMap[addr] = true
	}

	var filteredEvents []models.TransferEvent
	for _, event := range events {
		if activeAddressMap[event.ContractAddress] {
			filteredEvents = append(filteredEvents, event)
		}
	}

	log.Printf("Filtered %d events from %d total events based on active coin addresses",
		len(filteredEvents), len(events))

	return filteredEvents
}

// fallbackIndividualInserts attempts to insert records individually when batch insert fails
func (p *TransferProcessor) fallbackIndividualInserts(logs []*models.ContractCoinTransferLog) error {
	log.Printf("Attempting individual inserts for %d records as fallback", len(logs))

	successCount := 0
	failCount := 0

	for _, transferLog := range logs {
		err := models.CreateTransferLog(p.db, transferLog)
		if err != nil {
			log.Printf("Error creating individual transfer log for tx %s: %v", transferLog.TransactionHash, err)
			failCount++
			continue
		}
		successCount++
	}

	log.Printf("Fallback individual inserts completed: success=%d, failed=%d", successCount, failCount)

	if failCount > 0 {
		return fmt.Errorf("failed to insert %d out of %d records", failCount, len(logs))
	}

	return nil
}
