package database

import (
	"context"
	"fmt"
	"log"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"ipfun-coin-events/config"
	"ipfun-coin-events/models"
)

// DB holds the database connection
var DB *gorm.DB

// InitDatabase initializes the database connection
func InitDatabase(cfg *config.DatabaseConfig) error {
	dsn := cfg.GetDSN()

	// Configure GORM logger (use default for now)
	gormLogger := logger.Default.LogMode(logger.Warn)

	// Open database connection
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	// Get underlying sql.DB to configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// Configure connection pool
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(cfg.ConnMaxLifetime)

	// Test the connection
	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	// Set schema for models if specified
	if cfg.Schema != "" {
		models.SetSchema(cfg.Schema)
		log.Printf("Schema configured for models: %s", cfg.Schema)
	}

	DB = db
	log.Println("Database connection established successfully")

	return nil
}

// GetDB returns the database instance
func GetDB() *gorm.DB {
	return DB
}

// CloseDatabase closes the database connection
func CloseDatabase() error {
	if DB != nil {
		sqlDB, err := DB.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

// AutoMigrate runs database migrations
func AutoMigrate() error {
	if DB == nil {
		return fmt.Errorf("database not initialized")
	}

	// Note: We don't run auto-migrate for existing tables in production
	// This is just for reference. In production, you should use proper migrations.
	err := DB.AutoMigrate(
		&models.ContractCoinTransferLog{},
		&models.Group{},
	)

	if err != nil {
		return fmt.Errorf("failed to run migrations: %w", err)
	}

	log.Println("Database migrations completed successfully")
	return nil
}

// CreateSchema creates the schema if it doesn't exist
func CreateSchema(schemaName string) error {
	if DB == nil {
		return fmt.Errorf("database not initialized")
	}

	sql := fmt.Sprintf("CREATE SCHEMA IF NOT EXISTS %s", schemaName)
	err := DB.Exec(sql).Error
	if err != nil {
		return fmt.Errorf("failed to create schema %s: %w", schemaName, err)
	}

	log.Printf("Schema %s created or already exists", schemaName)
	return nil
}

// HealthCheck performs a database health check
func HealthCheck() error {
	if DB == nil {
		return fmt.Errorf("database not initialized")
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := sqlDB.PingContext(ctx); err != nil {
		return fmt.Errorf("database health check failed: %w", err)
	}

	return nil
}
