# Database Configuration
[database]
host = "localhost"
port = 5432
user = "postgres"
password = "postgres"
dbname = "postgres"
schema = "public"
sslmode = "require"
timezone = "UTC"
max_idle_conns = 5
max_open_conns = 10
conn_max_lifetime = "1h"

# Blockchain Configuration
[blockchain]
rpc_url = "https://bsc-dataseed.binance.org/"
chain_type = "BSC"
initial_block_height = 55587332
scan_batch_size = 1000
max_batch_size = 10000
confirmation_blocks = 12

# Application Configuration
[app]
log_level = "info"
scan_interval = "30s"
